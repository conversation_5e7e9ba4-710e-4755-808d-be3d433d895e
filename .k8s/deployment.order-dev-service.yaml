apiVersion: apps/v1
kind: Deployment
metadata:
  name: order-dev-service
  labels:
    app: order-dev-service
    env: config-map
spec:
  replicas: 1
  selector:
    matchLabels:
      app: order-dev-service
  template:
    metadata:
      labels:
        app: order-dev-service
    spec:
      imagePullSecrets:
        - name: regcred
      containers:
        - name: order-dev-service
          image: asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/order-service:250530111837
          command: ["./order-service"]
          args: ["start"]
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nexpos-dev-env
          livenessProbe:
            httpGet:
              path: /v1/order/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: order-dev-service
  labels:
    app: order-dev-service
spec:
  type: ClusterIP
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  selector:
    app: order-dev-service
---
apiVersion: v1
kind: Service
metadata:
  name: order-dev-lb
  labels:
    app: order-dev-service
spec:
  selector:
    app: order-dev-service
  ports:
    - port: 80
      targetPort: 3000
  type: LoadBalancer
