package router

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/mapping"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/bill"

	"github.com/gin-gonic/gin"
)

// DeliveryType represents the delivery method for an order
type DeliveryType string

// Constants for allowed delivery types
const (
	DeliveryTypePickUp   DeliveryType = "pick_up"
	DeliveryTypeDelivery DeliveryType = "delivery"
	DeliveryTypeTakeAway DeliveryType = "take_away"
)

type PaymentMethod string

// Constants for allowed payment methods
const (
	PaymentMethodCash  PaymentMethod = "CASH"
	PaymentMethodCOD   PaymentMethod = "COD"
	PaymentMethodVNPAY PaymentMethod = "VNPAY_QR"
	PaymentMethodMomo  PaymentMethod = "MOMO"
)

// PosCreateOrderRequest defines the request structure for creating a POS order
type WebCreateOrderRequest struct {
	SiteID        string        `json:"site_id" binding:"required"`
	CustomerPhone string        `json:"customer_phone,omitempty"`
	CustomerName  string        `json:"customer_name,omitempty"`
	DeliveryType  DeliveryType  `json:"delivery_type" binding:"required,oneof=pick_up delivery take_away"`
	PaymentMethod PaymentMethod `json:"payment_method" binding:"required,oneof=CASH COD VNPAY_QR MOMO"`
	VoucherCodes  []string      `json:"voucher_codes,omitempty"`
	// AppliedDiscount float64      `json:"applied_discount,omitempty"`
	OrderItems []struct {
		Code     string  `json:"code" binding:"required"`
		Quantity int     `json:"quantity" binding:"required,min=1"`
		Price    float64 `json:"price" binding:"required,min=0"`
		Note     string  `json:"note,omitempty"` // Note specific to this order item
	} `json:"order_items" binding:"required,min=1,dive"`
	CustomerAddress *struct {
		Lat  float64 `json:"lat" binding:"required_with=Long Text"`
		Long float64 `json:"long" binding:"required_with=Lat Text"`
		Text string  `json:"text" binding:"required_with=Lat Long"`
	} `json:"customer_address,omitempty" binding:"required_if=DeliveryType delivery"`
	PickupTime *string `json:"pickup_time,omitempty" binding:"required_if=DeliveryType pick_up"`
	Note       string  `json:"note,omitempty"`
}

// CreatePosOrder handles the request to create a new order through POS
func CreateWebOrder(c *gin.Context) {
	var req WebCreateOrderRequest

	// Validate request body against struct validation rules
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get database connection
	db := middlewares.GetDB(c)

	// Fetch site data
	var site models.Site
	if err := db.Where("id = ?", req.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "Site not found",
		})
		return
	}

	// Calculate subtotal and total
	var subtotal float64
	for _, item := range req.OrderItems {
		subtotal += item.Price * float64(item.Quantity)
	}

	total := subtotal // For POS orders, we assume no additional fees or taxes unless specified

	// Generate order ID
	// Format: {site_code}-{YYMMDD}-{index}
	now := time.Now()
	orderGroup := fmt.Sprintf("%s-%s", strings.ToUpper(site.Code), now.Format("060102"))

	// Get or create order index
	var siteOrderIndex models.SiteOrderIndex
	if err := db.Where("site_id = ? AND group_name = ?", req.SiteID, orderGroup).
		FirstOrCreate(&siteOrderIndex, models.SiteOrderIndex{
			SiteID:    req.SiteID,
			GroupName: orderGroup,
			Index:     0,
		}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to generate order ID",
		})
		return
	}

	// Increment index
	siteOrderIndex.Index++
	if err := db.Save(&siteOrderIndex).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to update order index",
		})
		return
	}

	orderID := fmt.Sprintf("%s-%03d", orderGroup, siteOrderIndex.Index)

	// Create order dishes
	dishes := make([]models.OrderDish, len(req.OrderItems))
	for i, item := range req.OrderItems {
		dishes[i] = models.OrderDish{
			Name:          item.Code, // Using code as name temporarily
			Quantity:      int64(item.Quantity),
			Price:         item.Price * float64(item.Quantity),
			DiscountPrice: item.Price * float64(item.Quantity), // No discount by default
			Note:          item.Note,                           // Use the note from each order item
		}
	}

	// Determine status based on payment method
	var status string
	switch req.PaymentMethod {
	case PaymentMethodCash:
		status = "FINISH"
	case PaymentMethodCOD:
		status = "PENDING"
	case PaymentMethodVNPAY, PaymentMethodMomo:
		status = "WAITING_PAYMENT"
	default:
		status = "PENDING" // Fallback status
	}

	// Create the order object with raw data
	rawOrderData := map[string]interface{}{
		"id":                 orderID,
		"order_id":           orderID,
		"source":             "local",
		"customer_name":      req.CustomerName,
		"customer_phone":     req.CustomerPhone,
		"customer_address":   req.CustomerAddress,
		"dishes":             dishes,
		"note":               req.Note,
		"total":              subtotal,
		"total_discount":     0,
		"total_for_biz":      total,
		"order_time":         now.Format(time.RFC3339),
		"order_time_sort":    now.Unix(),
		"delivery_time":      now.Format(time.RFC3339),
		"delivery_time_unix": now.Unix(),
		"pick_time":          now.Format(time.RFC3339),
		"raw":                req,
	}

	// Add payment information only for online payment methods (VNPAY or MOMO)
	if req.PaymentMethod == PaymentMethodVNPAY || req.PaymentMethod == PaymentMethodMomo {
		rawOrderData["payments"] = []models.OrderPayment{
			{
				Vendor:  string(req.PaymentMethod),
				Amount:  total,
				OrderID: orderID,
			},
		}
	}

	// Use the standard mapping approach with the sdk/mapping package
	merchantOrder := &models.MerchantOrder{
		LongOrderID:  orderID,
		ShortOrderID: orderID,
		Source:       "web",
		DataInDetail: rawOrderData,
	}

	// Get the data mapping using the mapLocalOrder function
	dataMapping := mapping.MapOrder(merchantOrder).DataMapping.Data

	// Create the order object
	order := models.Order{
		OrderID:      orderID,
		ShortOrderID: orderID,
		Source:       "web",
		SiteID:       req.SiteID,
		HubID:        fmt.Sprintf("%v", site.HubID),
		Status:       status,
		DataMapping: models.JSONField[models.DataMapping]{
			Data: dataMapping,
		},
		Data: models.JSONField[map[string]any]{
			Data: rawOrderData,
		},
		CreatedAt: &now,
		UpdatedAt: &now,
	}

	// Save the order to database
	if err := db.Create(&order).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to create order: " + err.Error(),
		})
		return
	}

	// if order is already paid, generate bills
	if req.PaymentMethod == PaymentMethodCash {
		totalPaidCompleted := total // In this simplified version, we assume all is paid

		// Send bills to printer and Zalo if applicable
		if totalPaidCompleted == total {
			// Generate kitchen bill and send to channel
			_, err := bill.GenerateOrderBills(db, orderID, bill.BillOptions{
				BillType:     "bill_for_kitchen",
				GenerateBill: true,
			})
			if err == nil {
				bill.SendOrderBillToChannel(db, orderID, bill.ChannelOptions{
					BillTemplate: "bill_for_kitchen",
					Bill:         true,
					Zalo:         true, // Send kitchen bills to Zalo
				})
			}

			// Generate complete bill and send to channel
			_, err = bill.GenerateOrderBills(db, orderID, bill.BillOptions{
				BillType:     "bill_for_complete",
				GenerateBill: true,
			})
			if err == nil {
				bill.SendOrderBillToChannel(db, orderID, bill.ChannelOptions{
					BillTemplate: "bill_for_complete",
					Bill:         true,
				})
			}
		}
	}

	// Return response
	c.JSON(http.StatusOK, gin.H{
		"success":        true,
		"order_id":       orderID,
		"message":        "Order created successfully",
		"payment_method": req.PaymentMethod,
		"status":         order.Status,
	})
}
