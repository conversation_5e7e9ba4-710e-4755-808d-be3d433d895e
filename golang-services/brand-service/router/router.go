package router

import (
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/gin-gonic/gin"
)

func LoadHandlers(r gin.IRouter) gin.IRouter {
	required := middlewares.RequireAuth
	requireAuthWithPermission := middlewares.AuthorizeWithPermission

	api := r.Group("v1/brand-service")
	{
		// Brand Management
		api.GET("health", HealthCheck)

		// Map API
		api.GET("map/suggest_addresses", GetMapSuggestAddressesV2)

		// Province API
		api.GET("map/provinces", GetProvinces)

		brandsGroup := api.Group("brands")
		{
			brandsGroup.GET("", requireAuthWithPermission(models.PermViewBrands), GetBrandList)
			brandsGroup.POST("", requireAuthWithPermission(models.PermCreateBrand), CreateBrand)
			brandsGroup.GET(":brand_id", requireAuthWithPermission(models.PermViewBrands), GetBrandDetail)
			brandsGroup.PUT(":brand_id", requireAuthWithPermission(models.PermUpdateBrand), UpdateBrand)
			brandsGroup.PUT(":brand_id/banners", requireAuthWithPermission(models.PermUpdateBrand), UpdateBanners)
			brandsGroup.DELETE(":brand_id", requireAuthWithPermission(models.PermDeleteBrand), DeleteBrand)

			// Brand Menu
			brandsGroup.GET(":brand_id/menu/categories", requireAuthWithPermission(models.PermManageMenu), GetBrandMenuCategoryList)
			brandsGroup.POST(":brand_id/menu/get_site_app_menu", requireAuthWithPermission(models.PermManageMenu), GetBrandMenuSiteAppMenu)
			brandsGroup.POST(":brand_id/menu/get_all_site_app_menu", requireAuthWithPermission(models.PermManageMenu), GetBrandMenuAllSiteAppMenu)
			brandsGroup.POST(":brand_id/menu/apply_site_app_menu", requireAuthWithPermission(models.PermManageMenu), PostBrandMenuFromAllSiteApps)
			brandsGroup.POST(":brand_id/menu/apply_menu_for_all_sites", requireAuthWithPermission(models.PermManageMenu), ApplyBrandMenuForAllSites)
			brandsGroup.POST(":brand_id/menu/categories", requireAuthWithPermission(models.PermManageMenu), CreateUpdateBrandMenuCategory)
			brandsGroup.DELETE(":brand_id/menu/category/:category_id", requireAuthWithPermission(models.PermManageMenu), DeleteBrandMenuCategory)

			// Brand Bills
			brandsGroup.GET(":brand_id/bills", requireAuthWithPermission(models.PermViewBills), GetBillList)
			brandsGroup.POST(":brand_id/bills", requireAuthWithPermission(models.PermCreateBill), CreateBill)
			brandsGroup.PUT(":brand_id/bills/:bill_id", requireAuthWithPermission(models.PermUpdateBill), UpdateBill)
			brandsGroup.POST(":brand_id/bills/:bill_id/delete", requireAuthWithPermission(models.PermDeleteBill), DeleteBill)

			// Core Products
			brandsGroup.GET(":brand_id/core-products/import-template", requireAuthWithPermission(models.PermViewProducts), GetImportTemplate)
			brandsGroup.POST(":brand_id/core-products/import", requireAuthWithPermission(models.PermCreateProduct), ImportCoreProducts)
			brandsGroup.GET(":brand_id/core-products/export", requireAuthWithPermission(models.PermViewProducts), ExportCoreProducts)
			brandsGroup.GET(":brand_id/core-products", requireAuthWithPermission(models.PermViewProducts), GetCoreProducts)
			brandsGroup.POST(":brand_id/core-products", requireAuthWithPermission(models.PermCreateProduct), CreateCoreProduct)
			brandsGroup.PUT(":brand_id/core-products/:code", requireAuthWithPermission(models.PermUpdateProduct), UpdateCoreProduct)
			brandsGroup.DELETE(":brand_id/core-products/:code", requireAuthWithPermission(models.PermDeleteProduct), DeleteCoreProduct)
			brandsGroup.GET(":brand_id/core-products/:code", requireAuthWithPermission(models.PermViewProducts), GetCoreProductDetails)

			// Core Product Categories
			brandsGroup.GET(":brand_id/core-product-categories", requireAuthWithPermission(models.PermViewProducts), GetCoreProductCategories)
			brandsGroup.POST(":brand_id/core-product-categories", requireAuthWithPermission(models.PermCreateProduct), CreateCoreProductCategory)
			brandsGroup.DELETE(":brand_id/core-product-categories/:id", requireAuthWithPermission(models.PermDeleteProduct), DeleteCoreProductCategory)

			// Core Product Sources
			brandsGroup.GET(":brand_id/core-product-sources", requireAuthWithPermission(models.PermViewProducts), GetCoreProductSources)
			brandsGroup.POST(":brand_id/core-product-sources", requireAuthWithPermission(models.PermCreateProduct), CreateCoreProductSource)
			brandsGroup.DELETE(":brand_id/core-product-sources/:id", requireAuthWithPermission(models.PermDeleteProduct), DeleteCoreProductSource)

			// Core Product Units
			brandsGroup.GET(":brand_id/core-product-units", requireAuthWithPermission(models.PermViewProducts), GetCoreProductUnits)
			brandsGroup.POST(":brand_id/core-product-units", requireAuthWithPermission(models.PermCreateProduct), CreateCoreProductUnit)
			brandsGroup.DELETE(":brand_id/core-product-units/:id", requireAuthWithPermission(models.PermDeleteProduct), DeleteCoreProductUnit)
		}

		{
			// Menu Templates
			brandsGroup := api.Group("/brands")
			{
				brandsGroup.GET(":brand_id/menu_templates", requireAuthWithPermission(models.PermManageMenu), GetMenuTemplates)
				brandsGroup.POST(":brand_id/menu_templates", requireAuthWithPermission(models.PermManageMenu), CreateTemplate)
				brandsGroup.PUT(":brand_id/menu_templates/:template_id", requireAuthWithPermission(models.PermManageMenu), UpdateTemplate)
				brandsGroup.DELETE(":brand_id/menu_templates/:template_id", requireAuthWithPermission(models.PermManageMenu), DeleteTemplate)
				brandsGroup.GET(":brand_id/menu_templates/:template_id", requireAuthWithPermission(models.PermManageMenu), GetTemplate)
			}

			// Menu Template Categories and Items
			menuTemplatesGroup := api.Group("/menu_templates")
			{
				menuTemplatesGroup.POST(":template_id/categories", requireAuthWithPermission(models.PermManageMenu), CreateCategory)
				menuTemplatesGroup.GET(":template_id/categories", requireAuthWithPermission(models.PermManageMenu), GetCategories)
				menuTemplatesGroup.PUT(":template_id/categories/:category_id", requireAuthWithPermission(models.PermManageMenu), UpdateCategory)
				menuTemplatesGroup.DELETE(":template_id/categories/:category_id", requireAuthWithPermission(models.PermManageMenu), DeleteCategory)
				menuTemplatesGroup.POST(":template_id/categories/:category_id/items", requireAuthWithPermission(models.PermManageMenu), CreateItem)
				menuTemplatesGroup.PUT(":template_id/categories/:category_id/items/:item_id", requireAuthWithPermission(models.PermManageMenu), UpdateItem)
				menuTemplatesGroup.DELETE(":template_id/categories/:category_id/items/:item_id", requireAuthWithPermission(models.PermManageMenu), DeleteItem)
				menuTemplatesGroup.POST(":template_id/option-categories", requireAuthWithPermission(models.PermManageMenu), CreateOptionCategory)
				menuTemplatesGroup.PUT(":template_id/option-categories/:id", requireAuthWithPermission(models.PermManageMenu), UpdateOptionCategory)
				menuTemplatesGroup.DELETE(":template_id/option-categories/:id", requireAuthWithPermission(models.PermManageMenu), DeleteOptionCategory)
				menuTemplatesGroup.POST(":template_id/apply", requireAuthWithPermission(models.PermManageMenu), ApplyTemplateToSite)
			}

			// Site Menu
			siteGroup := api.Group("/sites")
			{
				siteGroup.GET(":site_id/menu", GetMenu)
				siteGroup.POST(":site_id/menu/categories", requireAuthWithPermission(models.PermManageMenu), CreateSiteCategory)
				siteGroup.PUT(":site_id/menu/categories/:category_id", requireAuthWithPermission(models.PermManageMenu), UpdateSiteCategory)
				siteGroup.DELETE(":site_id/menu/categories/:category_id", requireAuthWithPermission(models.PermManageMenu), DeleteSiteCategory)
				siteGroup.POST(":site_id/menu/categories/:category_id/items", requireAuthWithPermission(models.PermManageMenu), CreateSiteItem)
				siteGroup.PUT(":site_id/menu/categories/:category_id/items/:item_id", requireAuthWithPermission(models.PermManageMenu), UpdateSiteItem)
				siteGroup.DELETE(":site_id/menu/categories/:category_id/items/:item_id", requireAuthWithPermission(models.PermManageMenu), DeleteSiteItem)
				siteGroup.POST(":site_id/menu/categories/:category_id/items/:item_id/channel", requireAuthWithPermission(models.PermManageMenu), UpdateItemSaleChannel)
				siteGroup.POST(":site_id/menu/bulk-update-channel", requireAuthWithPermission(models.PermManageMenu), BulkUpdateItemSaleChannel)
				siteGroup.DELETE(":site_id/menu/categories/:category_id/items/:item_id/channel/:channel", requireAuthWithPermission(models.PermManageMenu), DeleteItemSaleChannel)
				siteGroup.POST(":site_id/menu/publish", requireAuthWithPermission(models.PermManageMenu), PublishMenu)
				siteGroup.POST(":site_id/menu/option-categories", requireAuthWithPermission(models.PermManageMenu), CreateSiteOptionCategory)
				siteGroup.PUT(":site_id/menu/option-categories/:id", requireAuthWithPermission(models.PermManageMenu), UpdateSiteOptionCategory)
				siteGroup.DELETE(":site_id/menu/option-categories/:id", requireAuthWithPermission(models.PermManageMenu), DeleteSiteOptionCategory)
			}
		}

		// Hub Management
		api.GET("hubs", requireAuthWithPermission(models.PermViewHubs), GetHubList)
		api.GET("hubs/:hub_id", requireAuthWithPermission(models.PermViewHubs), GetHubDetail)
		api.POST("hubs", requireAuthWithPermission(models.PermCreateHub), CreateHub)
		api.PUT("hubs/:hub_id", requireAuthWithPermission(models.PermUpdateHub), UpdateHub)
		api.DELETE("hubs/:hub_id", requireAuthWithPermission(models.PermDeleteHub), DeleteHub)
		api.GET("hub/printers", requireAuthWithPermission(models.PermViewHubs), GetHubPrinters)
		api.POST("hubs/:hub_id/overview", requireAuthWithPermission(models.PermViewHubs), GetHubOverview)

		// Working Shifts (Hub-wide)
		api.GET("hub/working-shifts", required(), GetAllHubsWorkingShift)
		api.GET("hub/working-shift/reports", required(), GetWorkingShiftsReport)

		// Reports
		api.GET("reports", required(), GetReport)

		hubsGroup := api.Group("hubs/:hub_id")
		{
			// Working Shifts
			hubsGroup.GET("last-working-shifts", requireAuthWithPermission(models.PermViewHubs), GetLastWorkingShift)
			hubsGroup.GET("working-shifts", requireAuthWithPermission(models.PermViewHubs), GetWorkingShifts)
			hubsGroup.POST("working-shifts", requireAuthWithPermission(models.PermViewHubs), CreateWorkingShift)
			hubsGroup.PUT("working-shifts/:shift_id", requireAuthWithPermission(models.PermViewHubs), UpdateWorkingShift)
			hubsGroup.DELETE("working-shifts/:shift_id", requireAuthWithPermission(models.PermViewHubs), DeleteWorkingShift)
			hubsGroup.GET("working-shifts/:shift_id/prints", requireAuthWithPermission(models.PermViewHubs), PrintWorkingShift)

			hubsGroup.GET("prints", requireAuthWithPermission(models.PermViewHubs), GetHubPrintList)
		}

		// Site Management
		api.GET("brand/sites", requireAuthWithPermission(models.PermViewSites), GetSiteList)
		api.GET("brand/site/exports", requireAuthWithPermission(models.PermViewSites), GetSiteExports)
		api.GET("brand/site-details", GetSite)
		api.POST("brands/:brand_id/sites", requireAuthWithPermission(models.PermCreateSite), CreateSite)
		api.PUT("brands/:brand_id/sites/:site_id", requireAuthWithPermission(models.PermUpdateSite), UpdateSite)
		api.DELETE("brands/:brand_id/sites/:site_id", requireAuthWithPermission(models.PermDeleteSite), DeleteSite)

		siteGroup := api.Group("sites/:site_id")
		{
			// Site open status endpoints
			siteGroup.GET("open/status", required(), GetSiteOpenStatus)
			siteGroup.POST("opens", required(), UpdateSiteStoreOpen)
			siteGroup.GET("open/hours", required(), GetStoreOpeningHours)
			siteGroup.POST("open/hours", required(), UpdateStoreOpeningHours)
			siteGroup.POST("open/sync", required(), SyncStoreOpeningHours)

			// Site special times
			siteGroup.GET("special/hours", required(), GetStoreSpecialOpeningHours)
			siteGroup.POST("special/hours", required(), CreateStoreSpecialOpeningHours)
			siteGroup.PUT("special/hours/:id", required(), UpdateStoreSpecialOpeningHours)
			siteGroup.DELETE("special/hours/:id", required(), DeleteStoreSpecialOpeningHours)
			siteGroup.POST("special/hours/sync", required(), SyncStoreSpecialOpeningHours)

			// Integration endpoints
			siteGroup.POST("integration/apps/:app_id/login", requireAuthWithPermission(models.PermManageIntegration), LoginAppAccount)
			siteGroup.POST("integration/apps/momo/activate", requireAuthWithPermission(models.PermManageIntegration), ActiveMoMoAccountToSite)
			siteGroup.POST("integration/apps/momo/deactivate", requireAuthWithPermission(models.PermManageIntegration), DeActiveAppAccount)

			siteGroup.POST("integration/apps/:app_id/activate", requireAuthWithPermission(models.PermManageIntegration), ActiveAppAccount)
			siteGroup.POST("integration/apps/:app_id/deactivate", requireAuthWithPermission(models.PermManageIntegration), DeActiveAppAccount)
		}
		noSiteGroup := api.Group("brands/:brand_id/site")
		{
			noSiteGroup.POST("apps/:app_id/login", requireAuthWithPermission(models.PermManageIntegration), LoginAppAccount)
			noSiteGroup.POST("apps/:app_id/activate", requireAuthWithPermission(models.PermManageIntegration), ActiveQcomAppAccount)
		}
	}

	return r
}
