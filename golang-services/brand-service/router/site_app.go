// order-service/router/site.go
package router

import (
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/gin-gonic/gin"
)

type models.MoMo struct {
	Username     string   `json:"username"`
	Password     string   `json:"password"`
	ServiceTypes []string `json:"service_types"`
}

func ActiveMoMoAccountToSite(c *gin.Context) {
	db := middlewares.GetDB(c)

	siteID := c.Param("site_id")
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	var req activeAccountRequest
	var err error
	if err = c.ShouldBindJSON(&req); err != nil {
		c.<PERSON><PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}
